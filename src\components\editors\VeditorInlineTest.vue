<template>
  <div class="test-container">
    <h2>VeditorInline 自适应高度测试</h2>
    
    <div class="test-section">
      <h3>1. 固定高度模式（40px）</h3>
      <VeditorInline 
        v-model="content1" 
        :height="40"
        :auto-height="false"
        placeholder="固定高度40px，内容超出会显示滚动条"
      />
      <p class="content-info">内容长度: {{ content1?.length || 0 }} 字符</p>
    </div>
    
    <div class="test-section">
      <h3>2. 自适应高度模式（最大300px）</h3>
      <VeditorInline 
        v-model="content2" 
        :height="40"
        :auto-height="true"
        :max-height="300"
        placeholder="高度会根据内容自动调整，最大300px。试试输入多行内容！"
      />
      <p class="content-info">内容长度: {{ content2?.length || 0 }} 字符</p>
    </div>
    
    <div class="test-section">
      <h3>3. 自适应高度模式（最大150px）</h3>
      <VeditorInline 
        v-model="content3" 
        :height="40"
        :auto-height="true"
        :max-height="150"
        placeholder="最大高度150px"
      />
      <p class="content-info">内容长度: {{ content3?.length || 0 }} 字符</p>
    </div>
    
    <div class="debug-section">
      <h3>调试信息</h3>
      <p>请打开浏览器开发者工具的控制台查看调试信息</p>
      <button @click="addTestContent">添加测试内容</button>
      <button @click="clearContent">清空内容</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import VeditorInline from './VeditorInline.vue'

const content1 = ref('')
const content2 = ref('')
const content3 = ref('')

const addTestContent = () => {
  const testText = `这是第一行内容
这是第二行内容
这是第三行内容
这是第四行内容
这是第五行内容
这是第六行内容
这是第七行内容
这是第八行内容
这是第九行内容
这是第十行内容`
  
  content1.value = testText
  content2.value = testText
  content3.value = testText
}

const clearContent = () => {
  content1.value = ''
  content2.value = ''
  content3.value = ''
}
</script>

<style scoped>
.test-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;
}

.test-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.content-info {
  margin-top: 10px;
  font-size: 12px;
  color: #666;
}

.debug-section {
  margin-top: 30px;
  padding: 15px;
  background-color: #f0f0f0;
  border-radius: 8px;
}

.debug-section button {
  margin-right: 10px;
  padding: 8px 16px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.debug-section button:hover {
  background-color: #0056b3;
}
</style>
