<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高度自适应测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-container {
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 20px;
            position: relative;
        }
        
        .fixed-height {
            height: 40px;
            overflow: hidden;
        }
        
        .auto-height {
            min-height: 40px;
            max-height: 300px;
            overflow: auto;
        }
        
        .content {
            padding: 10px;
            line-height: 1.5;
            resize: none;
            border: none;
            outline: none;
            width: 100%;
            box-sizing: border-box;
        }
        
        .info {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>高度自适应测试</h1>
    
    <div class="test-section">
        <h3>1. 固定高度 (40px)</h3>
        <div class="test-container fixed-height">
            <textarea class="content" placeholder="固定高度40px，内容超出会被隐藏" oninput="updateInfo(this, 'info1')"></textarea>
        </div>
        <div class="info" id="info1">字符数: 0</div>
    </div>
    
    <div class="test-section">
        <h3>2. 自适应高度 (最大300px)</h3>
        <div class="test-container auto-height">
            <textarea class="content" placeholder="高度会根据内容自动调整，最大300px" oninput="updateInfo(this, 'info2')" style="height: auto; min-height: 40px;"></textarea>
        </div>
        <div class="info" id="info2">字符数: 0</div>
    </div>
    
    <div class="test-section">
        <h3>3. JavaScript 动态调整高度</h3>
        <div class="test-container" id="dynamic-container" style="height: 40px; overflow: auto;">
            <textarea class="content" id="dynamic-textarea" placeholder="JavaScript控制高度调整" oninput="adjustHeight(this)"></textarea>
        </div>
        <div class="info" id="info3">字符数: 0, 高度: 40px</div>
    </div>
    
    <button onclick="addTestContent()">添加测试内容</button>
    <button onclick="clearContent()">清空内容</button>
    
    <script>
        function updateInfo(textarea, infoId) {
            const info = document.getElementById(infoId);
            info.textContent = `字符数: ${textarea.value.length}`;
        }
        
        function adjustHeight(textarea) {
            const container = document.getElementById('dynamic-container');
            const info = document.getElementById('info3');
            
            // 临时设置高度为auto来获取内容高度
            textarea.style.height = 'auto';
            const contentHeight = textarea.scrollHeight;
            
            // 计算最终高度
            const minHeight = 40;
            const maxHeight = 300;
            const finalHeight = Math.max(minHeight, Math.min(contentHeight + 20, maxHeight));
            
            // 设置容器高度
            container.style.height = finalHeight + 'px';
            container.style.overflow = contentHeight + 20 > maxHeight ? 'auto' : 'hidden';
            
            // 更新信息
            info.textContent = `字符数: ${textarea.value.length}, 高度: ${finalHeight}px`;
        }
        
        function addTestContent() {
            const testText = `这是第一行内容
这是第二行内容
这是第三行内容
这是第四行内容
这是第五行内容
这是第六行内容
这是第七行内容
这是第八行内容
这是第九行内容
这是第十行内容`;
            
            document.querySelectorAll('textarea').forEach(textarea => {
                textarea.value = testText;
                if (textarea.id === 'dynamic-textarea') {
                    adjustHeight(textarea);
                }
                // 触发 oninput 事件
                textarea.dispatchEvent(new Event('input'));
            });
        }
        
        function clearContent() {
            document.querySelectorAll('textarea').forEach(textarea => {
                textarea.value = '';
                if (textarea.id === 'dynamic-textarea') {
                    adjustHeight(textarea);
                }
                // 触发 oninput 事件
                textarea.dispatchEvent(new Event('input'));
            });
        }
    </script>
</body>
</html>
