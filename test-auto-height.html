<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VeditorInline 自适应高度测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .instructions {
            background-color: #f0f8ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>VeditorInline 自适应高度功能测试</h1>
    
    <div class="instructions">
        <h3>测试说明</h3>
        <p>这个页面用于测试 VeditorInline 组件的自适应高度功能。请按照以下步骤测试：</p>
        <ol>
            <li>在第一个编辑器中输入内容，观察是否保持固定高度</li>
            <li>在第二个编辑器中输入多行内容，观察高度是否自动调整</li>
            <li>继续输入内容直到超过最大高度，观察是否出现滚动条</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>1. 固定高度模式（默认）</h3>
        <p>高度固定为150px，内容超出时显示滚动条</p>
        <!-- 这里需要在Vue应用中使用VeditorInline组件 -->
        <div style="border: 1px solid #ccc; height: 150px; padding: 10px; background: #f9f9f9;">
            <p>模拟固定高度编辑器 - 150px</p>
            <p>参数: :auto-height="false" :height="150"</p>
        </div>
    </div>

    <div class="test-section">
        <h3>2. 自适应高度模式</h3>
        <p>高度根据内容自动调整，最大300px</p>
        <div style="border: 1px solid #ccc; min-height: 40px; padding: 10px; background: #f0f8f0;">
            <p>模拟自适应高度编辑器</p>
            <p>参数: :auto-height="true" :max-height="300"</p>
            <p>试试输入多行内容...</p>
        </div>
    </div>

    <div class="test-section">
        <h3>3. 自适应高度 + 自定义最大高度</h3>
        <p>高度根据内容自动调整，最大200px</p>
        <div style="border: 1px solid #ccc; min-height: 40px; max-height: 200px; padding: 10px; background: #fff8f0; overflow: auto;">
            <p>模拟自适应高度编辑器（最大200px）</p>
            <p>参数: :auto-height="true" :max-height="200"</p>
        </div>
    </div>

    <script>
        // 这里可以添加一些JavaScript来模拟编辑器行为
        console.log('VeditorInline 自适应高度测试页面已加载');
    </script>
</body>
</html>
