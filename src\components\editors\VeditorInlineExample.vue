<template>
  <div class="example-container">
    <h3>VeditorInline 自适应高度示例</h3>
    
    <div class="example-section">
      <h4>固定高度模式（默认）</h4>
      <VeditorInline 
        v-model="content1" 
        :height="150" 
        placeholder="固定高度150px，内容超出会显示滚动条"
      />
    </div>
    
    <div class="example-section">
      <h4>自适应高度模式</h4>
      <VeditorInline
        v-model="content2"
        :auto-height="true"
        :max-height="300"
        placeholder="高度会根据内容自动调整，最大300px。试试输入多行内容看看效果！"
      />
      <p class="tip">💡 试试输入多行内容，观察编辑器高度的变化</p>
    </div>
    
    <div class="example-section">
      <h4>自适应高度 + 工具栏</h4>
      <VeditorInline 
        v-model="content3" 
        :auto-height="true"
        :max-height="200"
        :show-toolbar="true"
        placeholder="自适应高度，最大200px，带工具栏"
      />
    </div>
    
    <div class="debug-info">
      <h4>内容预览</h4>
      <div class="content-preview">
        <strong>内容1:</strong> {{ content1 || '空' }}
      </div>
      <div class="content-preview">
        <strong>内容2:</strong> {{ content2 || '空' }}
      </div>
      <div class="content-preview">
        <strong>内容3:</strong> {{ content3 || '空' }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import VeditorInline from './VeditorInline.vue'

const content1 = ref('')
const content2 = ref('')
const content3 = ref('')
</script>

<style scoped>
.example-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;
}

.example-section h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.debug-info {
  margin-top: 30px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.content-preview {
  margin-bottom: 10px;
  padding: 8px;
  background-color: white;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  word-break: break-all;
}

.tip {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
  font-style: italic;
}
</style>
