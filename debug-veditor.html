<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VeditorInline 调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        
        .debug-info {
            background-color: #f0f8ff;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 12px;
        }
        
        .editor-container {
            border: 2px solid #007bff;
            border-radius: 4px;
            margin: 10px 0;
            position: relative;
        }
        
        .editor-container::before {
            content: attr(data-height);
            position: absolute;
            top: -20px;
            right: 0;
            background: #007bff;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
        }
        
        button {
            margin: 5px;
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>VeditorInline 自适应高度调试</h1>
    
    <div class="test-section">
        <h3>调试说明</h3>
        <p>这个页面用于调试VeditorInline组件的自适应高度功能。</p>
        <p>请打开浏览器开发者工具的控制台查看详细的调试信息。</p>
        <p>查找以 "🔍 adjustHeight:" 开头的日志信息。</p>
    </div>
    
    <div class="test-section">
        <h3>测试步骤</h3>
        <ol>
            <li>在下面的编辑器中输入内容</li>
            <li>观察编辑器高度是否发生变化</li>
            <li>查看控制台的调试信息</li>
            <li>检查DOM元素的样式变化</li>
        </ol>
        
        <button onclick="addTestContent()">添加测试内容</button>
        <button onclick="clearContent()">清空内容</button>
        <button onclick="inspectEditor()">检查编辑器状态</button>
    </div>
    
    <div class="test-section">
        <h3>模拟编辑器容器</h3>
        <p>这是一个模拟的编辑器容器，用于测试高度调整逻辑：</p>
        
        <div class="editor-container" id="mock-editor" data-height="40px">
            <div style="padding: 10px; min-height: 40px; border: 1px solid #ccc;">
                <textarea 
                    id="mock-textarea" 
                    style="width: 100%; border: none; outline: none; resize: none; font-family: inherit;"
                    placeholder="在这里输入内容测试高度变化..."
                    oninput="mockAdjustHeight()"
                ></textarea>
            </div>
        </div>
        
        <div class="debug-info" id="mock-debug">
            高度: 40px, 内容长度: 0
        </div>
    </div>
    
    <div class="test-section">
        <h3>实际问题分析</h3>
        <div class="debug-info">
            <strong>可能的问题原因：</strong><br>
            1. CSS样式被其他规则覆盖<br>
            2. Vditor内部结构与预期不符<br>
            3. 高度计算逻辑有误<br>
            4. DOM更新时机问题<br>
            5. 响应式绑定失效
        </div>
    </div>
    
    <script>
        // 模拟高度调整函数
        function mockAdjustHeight() {
            const textarea = document.getElementById('mock-textarea');
            const container = document.getElementById('mock-editor');
            const debug = document.getElementById('mock-debug');
            
            if (!textarea || !container || !debug) return;
            
            // 临时设置高度为auto获取内容高度
            textarea.style.height = 'auto';
            const contentHeight = textarea.scrollHeight;
            
            // 计算最终高度
            const minHeight = 40;
            const maxHeight = 300;
            const finalHeight = Math.max(minHeight, Math.min(contentHeight + 20, maxHeight));
            
            // 设置容器高度
            container.style.height = finalHeight + 'px';
            container.setAttribute('data-height', finalHeight + 'px');
            
            // 设置textarea高度
            textarea.style.height = (finalHeight - 20) + 'px';
            
            // 更新调试信息
            debug.innerHTML = `高度: ${finalHeight}px, 内容长度: ${textarea.value.length}, scrollHeight: ${contentHeight}`;
            
            console.log('🔍 mockAdjustHeight:', {
                contentHeight,
                finalHeight,
                textLength: textarea.value.length
            });
        }
        
        function addTestContent() {
            const textarea = document.getElementById('mock-textarea');
            if (textarea) {
                textarea.value = `这是第一行测试内容
这是第二行测试内容
这是第三行测试内容
这是第四行测试内容
这是第五行测试内容
这是第六行测试内容
这是第七行测试内容
这是第八行测试内容`;
                mockAdjustHeight();
            }
        }
        
        function clearContent() {
            const textarea = document.getElementById('mock-textarea');
            if (textarea) {
                textarea.value = '';
                mockAdjustHeight();
            }
        }
        
        function inspectEditor() {
            console.log('🔍 检查页面中的编辑器元素...');
            
            // 查找所有可能的编辑器容器
            const containers = document.querySelectorAll('[id*="vditor"], .vditor-inline-container, .vditor');
            console.log('🔍 找到的编辑器容器:', containers);
            
            containers.forEach((container, index) => {
                console.log(`🔍 容器 ${index}:`, {
                    id: container.id,
                    className: container.className,
                    height: container.style.height,
                    offsetHeight: container.offsetHeight,
                    scrollHeight: container.scrollHeight
                });
                
                // 查找内部元素
                const resetElements = container.querySelectorAll('.vditor-reset');
                resetElements.forEach((reset, resetIndex) => {
                    console.log(`🔍 容器 ${index} 的 reset 元素 ${resetIndex}:`, {
                        offsetHeight: reset.offsetHeight,
                        scrollHeight: reset.scrollHeight,
                        clientHeight: reset.clientHeight
                    });
                });
            });
        }
        
        // 页面加载完成后执行检查
        window.addEventListener('load', () => {
            console.log('🔍 页面加载完成，开始检查...');
            setTimeout(inspectEditor, 1000);
        });
    </script>
</body>
</html>
