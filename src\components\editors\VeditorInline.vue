<script setup lang="ts">
import Vditor from "vditor";
import "vditor/dist/index.css";
import {
  ref,
  onMounted,
  defineProps,
  defineExpose,
  defineModel,
  watch,
  withDefaults,
  nextTick,
} from "vue";

interface VeditorInlineProps {
  disabled?: boolean;
  height?: number;
  mode?: "ir" | "wysiwyg" | "sv";
  placeholder?: string;
  showToolbar?: boolean; // 控制工具栏是否显示
  autoHeight?: boolean; // 控制是否自适应高度
  maxHeight?: number; // 最大高度限制
}

const props = withDefaults(defineProps<VeditorInlineProps>(), {
  disabled: false,
  height: 150, // inline版本默认高度更小
  mode: "ir", //
  placeholder: "请输入",
  showToolbar: false, // 默认不显示工具栏
  autoHeight: false, // 默认不启用自适应高度
  maxHeight: 300, // 默认最大高度300px
});

const vditor = ref<Vditor>();
const model = defineModel<string>();
const isReady = ref(false);
const currentHeight = ref(props.autoHeight ? Math.max(40, props.height) : props.height);

// 添加唯一ID生成
const editorId = ref(`vditor-${Math.random().toString(36).substring(2, 11)}`);

watch(
  () => model.value,
  (newVal) => {
    if (vditor.value && isReady.value && newVal !== vditor.value.getValue()) {
      vditor.value.setValue(newVal || "");
    }
  },
  { deep: true }
);

// 自适应高度调整函数
const adjustHeight = async () => {
  if (!props.autoHeight || !vditor.value || !isReady.value) return;

  await nextTick(); // 确保DOM更新完成

  const editor = document.getElementById(editorId.value);
  if (!editor) {
    console.log('🔍 adjustHeight: 找不到编辑器容器', editorId.value);
    return;
  }

  // 获取编辑器的内容区域，尝试多种选择器
  const selectors = [
    '.vditor-reset',
    '.vditor-ir .vditor-reset',
    '.vditor-wysiwyg .vditor-reset',
    '.vditor-sv .vditor-reset',
    '.vditor-content',
    '.vditor-ir',
    '.vditor-wysiwyg',
    '.vditor-sv'
  ];

  let contentElement: HTMLElement | null = null;
  let usedSelector = '';
  let maxElementHeight = 0;

  // 尝试所有选择器，找到高度最大的元素
  for (const selector of selectors) {
    const element = editor.querySelector(selector) as HTMLElement;
    if (element) {
      const elementHeight = Math.max(element.scrollHeight, element.offsetHeight, element.clientHeight);
      console.log(`🔍 检查选择器 ${selector}:`, {
        scrollHeight: element.scrollHeight,
        offsetHeight: element.offsetHeight,
        clientHeight: element.clientHeight,
        maxHeight: elementHeight
      });

      if (elementHeight > maxElementHeight) {
        maxElementHeight = elementHeight;
        contentElement = element;
        usedSelector = selector;
      }
    }
  }

  if (!contentElement) {
    console.log('🔍 adjustHeight: 找不到内容元素，尝试的选择器:', selectors);
    return;
  }

  console.log('🔍 adjustHeight: 使用选择器:', usedSelector, '高度:', maxElementHeight);

  // 如果所有元素的高度都是0，说明内容还没有渲染完成，使用文本长度估算
  let contentHeight = maxElementHeight;
  if (contentHeight === 0 && model.value) {
    // 根据文本长度估算高度
    const textLength = model.value.length;
    const lineHeight = 24; // 假设行高24px
    const charsPerLine = 50; // 假设每行50个字符
    const estimatedLines = Math.ceil(textLength / charsPerLine);
    contentHeight = Math.max(estimatedLines * lineHeight, 50);
    console.log('🔍 使用文本长度估算高度:', {
      textLength,
      estimatedLines,
      contentHeight
    });
  }

  // 如果内容高度太小，使用最小高度
  const minHeight = 50;
  const maxAllowedHeight = props.maxHeight;
  const finalHeight = Math.max(minHeight, Math.min(contentHeight + 10, maxAllowedHeight));

  console.log('🔍 adjustHeight: 计算结果', {
    contentHeight,
    finalHeight,
    minHeight,
    maxAllowedHeight
  });

  // 更新响应式高度值
  currentHeight.value = finalHeight;

  // 直接设置DOM样式，确保样式生效
  const container = editor as HTMLElement;
  if (container) {
    // 使用 setProperty 方法强制设置样式
    container.style.setProperty('height', `${finalHeight}px`, 'important');
    container.style.setProperty('min-height', `${finalHeight}px`, 'important');
    container.style.setProperty('max-height', `${maxAllowedHeight}px`, 'important');
    container.style.setProperty('overflow', contentHeight + 10 > maxAllowedHeight ? 'auto' : 'hidden', 'important');

    console.log('🔍 adjustHeight: 设置容器样式完成', {
      height: container.style.height,
      minHeight: container.style.minHeight,
      maxHeight: container.style.maxHeight,
      computedHeight: window.getComputedStyle(container).height
    });
  }

  // 同时设置内部元素的高度
  const vditorContent = editor.querySelector('.vditor-content') as HTMLElement;
  if (vditorContent) {
    vditorContent.style.setProperty('height', `${finalHeight}px`, 'important');
    console.log('🔍 设置 vditor-content 高度:', vditorContent.style.height);
  }

  const vditorEditor = editor.querySelector('.vditor-ir, .vditor-wysiwyg, .vditor-sv') as HTMLElement;
  if (vditorEditor) {
    vditorEditor.style.setProperty('height', `${finalHeight}px`, 'important');
    console.log('🔍 设置 vditor-editor 高度:', vditorEditor.style.height);
  }

  // 强制设置所有可能的内部元素
  const allInternalElements = editor.querySelectorAll('.vditor-reset, .vditor-ir, .vditor-wysiwyg, .vditor-sv, .vditor-content');
  allInternalElements.forEach((element, index) => {
    const htmlElement = element as HTMLElement;
    htmlElement.style.setProperty('height', `${finalHeight}px`, 'important');
    htmlElement.style.setProperty('min-height', `${finalHeight}px`, 'important');
    console.log(`🔍 强制设置内部元素 ${index} (${element.className}) 高度:`, htmlElement.style.height);
  });
};

onMounted(() => {
  console.log('🔍 VeditorInline 组件挂载', {
    editorId: editorId.value,
    autoHeight: props.autoHeight,
    height: props.height,
    maxHeight: props.maxHeight,
    currentHeight: currentHeight.value
  });

  if (model.value) {
    model.value += " ";
  }

  vditor.value = new Vditor(editorId.value, {
    // 使用动态生成的ID
    height: props.autoHeight ? currentHeight.value : props.height,
    width: "100%",
    mode: props.mode,
    cache: {
      enable: false,
    },
    minHeight: props.autoHeight ? 40 : props.height,
    resize: {
      enable: false,
    },
    preview: {
      math: {
        engine: "KaTeX",
      },
      markdown: {
        sanitize: true,
        autoSpace: true,
        paragraphBeginningSpace: true,
      },
    },
    toolbar: props.showToolbar
      ? [
          "headings",
          "bold",
          "italic",
          "strike",
          "link",
          "|",
          "list",
          "ordered-list",
          "check",
          "outdent",
          "indent",
          "|",
          "quote",
          "line",
          "code",
          "inline-code",
          "table",
          "edit-mode",
          "both",
          // "|",
          // "upload",

          // "|",
          // "undo",
          // "redo",
          // "|",

          "preview",
          // "outline",
          // "code-theme",
          // "export",
        ]
      : [],
    hint: {
      extend: [], // 禁用扩展提示
    },
    comment: {
      enable: false, // 禁用评论功能
    },
    after: () => {
      console.log('🔍 Vditor after 回调触发', {
        autoHeight: props.autoHeight,
        editorId: editorId.value,
        maxHeight: props.maxHeight
      });

      isReady.value = true;
      if (model.value) {
        vditor.value?.setValue(model.value.trim());
      }

      // 自适应高度功能
      if (props.autoHeight) {
        console.log('🔍 开始自适应高度调整');
        // 多次延迟调整高度，确保DOM完全渲染和内容加载
        setTimeout(() => {
          console.log('🔍 第一次调整高度 (50ms)');
          adjustHeight();
        }, 50);
        setTimeout(() => {
          console.log('🔍 第二次调整高度 (200ms)');
          adjustHeight();
        }, 200);
        setTimeout(() => {
          console.log('🔍 第三次调整高度 (500ms)');
          adjustHeight();
        }, 500);
      } else {
        console.log('🔍 自适应高度未启用');
      }

      // 修改事件监听，使用动态ID，只有在显示工具栏时才添加事件监听
      if (props.showToolbar) {
        const editor = document.getElementById(editorId.value);
        if (editor) {
          editor.addEventListener("click", () => {
            editor.classList.add("toolbar-visible");
          });

          editor.addEventListener(
            "blur",
            (event) => {
              const toolbar = editor.querySelector(".vditor-toolbar");
              if (toolbar && !toolbar.contains(event.relatedTarget as Node)) {
                editor.classList.remove("toolbar-visible");
              }
            },
            true
          );
        }
      }
    },
    input: (value: string) => {
      console.log('🔍 input 事件触发', {
        valueLength: value.length,
        autoHeight: props.autoHeight,
        isReady: isReady.value
      });

      model.value = value;
      // 当内容变化时，如果启用了自适应高度，则调整高度
      if (props.autoHeight && isReady.value) {
        console.log('🔍 input 事件触发高度调整');
        setTimeout(() => adjustHeight(), 0);
      }
    },
    placeholder: props.placeholder,
  });

  if (props.disabled) {
    vditor.value.disabled();
  }
});

const getData = (): string => {
  if (vditor.value) {
    return vditor.value.getValue();
  }
  return "";
};

const setData = (value: string, dis?: boolean) => {
  if (vditor.value && isReady.value) {
    vditor.value.setValue(value);
    if (dis !== undefined) {
      if (dis) {
        vditor.value.disabled();
      } else {
        vditor.value.enable();
      }
    }
  }
};

// 调试函数
const debugEditor = () => {
  console.log('🔍 调试编辑器状态');
  const editor = document.getElementById(editorId.value);
  if (editor) {
    console.log('🔍 编辑器容器:', {
      id: editor.id,
      className: editor.className,
      offsetHeight: editor.offsetHeight,
      scrollHeight: editor.scrollHeight,
      style: {
        height: editor.style.height,
        minHeight: editor.style.minHeight,
        maxHeight: editor.style.maxHeight
      }
    });

    // 检查所有内部元素
    const allElements = editor.querySelectorAll('*');
    allElements.forEach((el, index) => {
      if (el.className.includes('vditor')) {
        const htmlEl = el as HTMLElement;
        console.log(`🔍 内部元素 ${index}:`, {
          className: el.className,
          offsetHeight: htmlEl.offsetHeight,
          scrollHeight: htmlEl.scrollHeight,
          height: htmlEl.style.height
        });
      }
    });
  }

  return {
    editorId: editorId.value,
    autoHeight: props.autoHeight,
    currentHeight: currentHeight.value,
    isReady: isReady.value
  };
};

// 将调试函数暴露到全局，方便在控制台调用
if (typeof window !== 'undefined') {
  (window as any)[`debugVeditor_${editorId.value}`] = debugEditor;
  console.log(`🔍 调试函数已注册: window.debugVeditor_${editorId.value}()`);
}

defineExpose({
  getData,
  setData,
  debugEditor,
  adjustHeight,
});
</script>

<template>
  <!-- 使用动态生成的ID -->
  <div
    :id="editorId"
    class="vditor-inline-container"
    :data-auto-height="props.autoHeight"
  ></div>
</template>

<style scoped>
.vditor-inline-container {
  border: 1px solid rgb(220, 223, 230);
  border-radius: 2px;
  position: relative;
}

/* 当启用自适应高度时，不设置固定高度 */
.vditor-inline-container:not([data-auto-height="true"]) {
  height: v-bind('props.height + "px"');
  overflow: hidden;
}

/* 编辑器内容区域样式 */
:deep(.vditor-content) {
  margin-top: 0;
  padding: 0 !important;
  min-height: unset !important;
  height: 100% !important;
  overflow: hidden;
}

/* 设置内部编辑区域样式 */
:deep(.vditor-ir),
:deep(.vditor-wysiwyg),
:deep(.vditor-sv) {
  min-height: unset !important;
  height: 100% !important;
  padding: 0 !important;
}

/* 重置内容区域样式 */
:deep(.vditor-reset) {
  padding: 5px 10px !important;
  line-height: 1.5;
  width: 100% !important;
  /* 修复列表样式 */
  & ul {
    list-style-type: disc !important;
    padding-left: 20px !important;
  }

  & ol {
    list-style-type: decimal !important;
    padding-left: 20px !important;
  }

  /* 修复列表项标记 */
  & li::marker {
    content: initial !important;
    color: initial !important;
  }
}

/* 移除所有滚动条相关样式 */

/* 工具栏样式保持不变 */
:deep(.vditor-toolbar) {
  position: absolute;
  left: -1px;
  right: -1px;
  bottom: 100%;
  z-index: 1000;
  background-color: #fff;
  border: 1px solid #ccced1;
  border-bottom: none;
  border-radius: 5px 5px 0 0;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  margin-bottom: -1px;
}

.toolbar-visible :deep(.vditor-toolbar) {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.toolbar-visible {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

/* 添加以下样式来隐藏面板 */
:deep(.vditor-panel) {
  display: none !important;
}
</style>
