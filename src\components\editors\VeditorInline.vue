<script setup lang="ts">
import Vditor from "vditor";
import "vditor/dist/index.css";
import {
  ref,
  onMounted,
  defineProps,
  defineExpose,
  defineModel,
  watch,
  withDefaults,
} from "vue";

interface VeditorInlineProps {
  disabled?: boolean;
  height?: number;
  mode?: "ir" | "wysiwyg" | "sv";
  placeholder?: string;
  showToolbar?: boolean; // 控制工具栏是否显示
}

const props = withDefaults(defineProps<VeditorInlineProps>(), {
  disabled: false,
  height: 150, // inline版本默认高度更小
  mode: "ir", //
  placeholder: "请输入",
  showToolbar: false, // 默认不显示工具栏
});

const vditor = ref<Vditor>();
const model = defineModel<string>();
const isReady = ref(false);

// 添加唯一ID生成
const editorId = ref(`vditor-${Math.random().toString(36).substr(2, 9)}`);

watch(
  () => model.value,
  (newVal) => {
    if (vditor.value && isReady.value && newVal !== vditor.value.getValue()) {
      vditor.value.setValue(newVal || "");
    }
  },
  { deep: true }
);

onMounted(() => {
  if (model.value) {
    model.value += " ";
  }

  vditor.value = new Vditor(editorId.value, {
    // 使用动态生成的ID
    height: props.height,
    width: "100%",
    mode: props.mode,
    cache: {
      enable: false,
    },
    minHeight: props.height,
    resize: {
      enable: false,
    },
    preview: {
      math: {
        engine: "KaTeX",
      },
      markdown: {
        sanitize: true,
        autoSpace: true,
        paragraphBeginningSpace: true,
      },
    },
    toolbar: props.showToolbar
      ? [
          "headings",
          "bold",
          "italic",
          "strike",
          "link",
          "|",
          "list",
          "ordered-list",
          "check",
          "outdent",
          "indent",
          "|",
          "quote",
          "line",
          "code",
          "inline-code",
          "table",
          "edit-mode",
          "both",
          // "|",
          // "upload",

          // "|",
          // "undo",
          // "redo",
          // "|",

          "preview",
          // "outline",
          // "code-theme",
          // "export",
        ]
      : [],
    hint: {
      extend: [], // 禁用扩展提示
    },
    comment: {
      enable: false, // 禁用评论功能
    },
    after: () => {
      isReady.value = true;
      if (model.value) {
        vditor.value?.setValue(model.value.trim());
      }

      // 修改事件监听，使用动态ID，只有在显示工具栏时才添加事件监听
      if (props.showToolbar) {
        const editor = document.getElementById(editorId.value);
        if (editor) {
          editor.addEventListener("click", () => {
            editor.classList.add("toolbar-visible");
          });

          editor.addEventListener(
            "blur",
            (event) => {
              const toolbar = editor.querySelector(".vditor-toolbar");
              if (toolbar && !toolbar.contains(event.relatedTarget as Node)) {
                editor.classList.remove("toolbar-visible");
              }
            },
            true
          );
        }
      }
    },
    input: (value: string) => {
      model.value = value;
    },
    placeholder: props.placeholder,
  });

  if (props.disabled) {
    vditor.value.disabled();
  }
});

const getData = (): string => {
  if (vditor.value) {
    return vditor.value.getValue();
  }
  return "";
};

const setData = (value: string, dis?: boolean) => {
  if (vditor.value && isReady.value) {
    vditor.value.setValue(value);
    if (dis !== undefined) {
      if (dis) {
        vditor.value.disabled();
      } else {
        vditor.value.enable();
      }
    }
  }
};

defineExpose({
  getData,
  setData,
});
</script>

<template>
  <!-- 使用动态生成的ID -->
  <div :id="editorId" class="vditor-inline-container"></div>
</template>

<style scoped>
.vditor-inline-container {
  border: 1px solid rgb(220, 223, 230);
  border-radius: 2px;
  position: relative;
}

/* 编辑器内容区域样式 */
:deep(.vditor-content) {
  margin-top: 0;
  padding: 0 !important;
  min-height: unset !important;
  height: v-bind('props.height + "px"') !important;
  overflow: hidden;
}

/* 设置内部编辑区域样式 */
:deep(.vditor-ir),
:deep(.vditor-wysiwyg),
:deep(.vditor-sv) {
  min-height: unset !important;
  height: v-bind('props.height + "px"') !important;
  padding: 0 !important;
}

/* 重置内容区域样式 */
:deep(.vditor-reset) {
  padding: 5px 10px !important;
  line-height: 1.5;
  width: 100% !important;
  /* 修复列表样式 */
  & ul {
    list-style-type: disc !important;
    padding-left: 20px !important;
  }

  & ol {
    list-style-type: decimal !important;
    padding-left: 20px !important;
  }

  /* 修复列表项标记 */
  & li::marker {
    content: initial !important;
    color: initial !important;
  }
}

/* 移除所有滚动条相关样式 */

/* 工具栏样式保持不变 */
:deep(.vditor-toolbar) {
  position: absolute;
  left: -1px;
  right: -1px;
  bottom: 100%;
  z-index: 1000;
  background-color: #fff;
  border: 1px solid #ccced1;
  border-bottom: none;
  border-radius: 5px 5px 0 0;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  margin-bottom: -1px;
}

.toolbar-visible :deep(.vditor-toolbar) {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.toolbar-visible {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

/* 添加以下样式来隐藏面板 */
:deep(.vditor-panel) {
  display: none !important;
}
</style>
